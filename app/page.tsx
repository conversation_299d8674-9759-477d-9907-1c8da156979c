import Link from "next/link"
import { ChevronRight, Calendar, Clock, Phone, Mail, CheckCircle2, Spark<PERSON>, Ticket } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { FactCard } from "@/components/fact-card"
import { ContactForm } from "@/components/contact-form"
import { Newsletter } from "@/components/newsletter"
import { NavBar } from "@/components/nav-bar"

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      {/* Navigation */}
      <NavBar />

      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative">
          <div className="absolute inset-0 z-10 bg-black/40" />
          <div className="h-[600px] bg-cover bg-center" style={{ backgroundImage: "url('/images/bananas1.jpeg')" }} />
          <div className="container absolute inset-0 z-20 flex flex-col items-center justify-center text-center text-white">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">The History of Bananas</h1>
            <p className="mt-6 max-w-lg text-lg">Explore the fascinating journey of the world's most popular fruit.</p>
            <div className="mt-8 flex flex-wrap justify-center gap-4">
              <Link href="#about">
                <Button size="lg" className="bg-green-600 hover:bg-green-700">
                  Discover the Story
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* Featured Stories Section */}
        <section id="products" className="py-20 bg-green-50">
          <div className="container">
            <div className="mb-12 flex flex-col items-center text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Follow the Journey</h2>
              <p className="mt-4 max-w-2xl text-lg text-muted-foreground">
                From a fragile species to a global commodity, discover the incredible story of the banana.
              </p>
            </div>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
              {/* Story Card 1: Endangered */}
              <Link href="/story" className="group block h-full">
                <Card className="relative h-full overflow-hidden transition-all hover:shadow-lg bg-gradient-to-br from-red-50 to-red-100">
                  <CardContent className="relative z-10 flex h-full flex-col justify-end p-4 text-left">
                    <h3 className="text-lg font-bold text-white">
                      Help our endangered precious fruit live on through the ages.
                    </h3>
                    <p className="mt-1 text-sm text-amber-300">Click to learn the story</p>
                  </CardContent>
                </Card>
              </Link>

              {/* Story Card 2: Origin */}
              <Link href="/origin" className="group block h-full">
                <Card className="relative h-full overflow-hidden transition-all hover:shadow-lg">
                  <div
                    className="absolute inset-0 bg-cover bg-center transition-transform duration-300 group-hover:scale-105"
                    style={{ backgroundImage: "url('/images/bread.jpg')" }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent" />
                  <CardContent className="relative z-10 flex h-full flex-col items-center justify-center p-6 text-center">
                    <h3 className="font-great-vibes text-4xl leading-tight text-white">
                      Where does the banana you eat come from?
                    </h3>
                  </CardContent>
                </Card>
              </Link>

              {/* Story Card 3: Packing */}
              <Link href="/packing" className="group block h-full">
                <Card className="relative h-full overflow-hidden transition-all hover:shadow-lg">
                  <div
                    className="absolute inset-0 bg-cover bg-center transition-transform duration-300 group-hover:scale-105"
                    style={{ backgroundImage: "url('/images/honey.jpg')" }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent" />
                  <CardContent className="relative z-10 flex h-full flex-col items-center justify-center p-6 text-center">
                    <h3 className="text-xl font-semibold leading-tight text-white">
                      How are my bananas picked and packed for safe travel?
                    </h3>
                  </CardContent>
                </Card>
              </Link>

              {/* Story Card 4: Shipping */}
              <Link href="/shipping" className="group block h-full">
                <Card className="relative h-full overflow-hidden transition-all hover:shadow-lg">
                  <div
                    className="absolute inset-0 bg-cover bg-center transition-transform duration-300 group-hover:scale-105"
                    style={{ backgroundImage: "url('/images/tomatoes.jpg')" }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent" />
                  <CardContent className="relative z-10 flex h-full flex-col items-center justify-center p-6 text-center">
                    <h3 className="text-xl font-semibold leading-tight text-white">
                      How do my bananas get to my favorite store?
                    </h3>
                  </CardContent>
                </Card>
              </Link>
            </div>
          </div>
        </section>

        {/* Health Benefits Section */}
        <section id="about" className="py-20">
          <div className="container">
            {/* Featured Art Image */}
            <div className="mb-16 flex justify-center">
              <div className="relative overflow-hidden rounded-2xl shadow-2xl">
                <img
                  src="/images/art1.png"
                  alt="Artistic representation of banana benefits and nutrition"
                  className="h-auto w-full max-w-4xl object-cover transition-transform duration-300 hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
              </div>
            </div>

            <div className="grid grid-cols-1 items-center gap-12 md:grid-cols-2">
              <div>
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Why are bananas good for me?</h2>
                <p className="mt-4 text-lg text-muted-foreground">
                  More than just a convenient and tasty snack, bananas are a powerhouse of essential nutrients that can
                  have a significant impact on your health and well-being. They are one of nature's most perfect foods.
                </p>
                <ul className="mt-6 space-y-4 text-lg text-muted-foreground">
                  <li className="flex items-start">
                    <CheckCircle2 className="mr-3 mt-1 h-6 w-6 flex-shrink-0 text-green-600" />
                    <span>
                      <strong>Packed with Potassium:</strong> Crucial for heart health and blood pressure regulation, a
                      medium-sized banana provides about 10% of your daily potassium needs.
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="mr-3 mt-1 h-6 w-6 flex-shrink-0 text-green-600" />
                    <span>
                      <strong>Energy Boosting:</strong> With their blend of complex carbs, natural sugars, and fiber,
                      bananas provide a sustained energy release, making them a perfect pre-workout snack.
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="mr-3 mt-1 h-6 w-6 flex-shrink-0 text-green-600" />
                    <span>
                      <strong>Digestive Health:</strong> The fiber in bananas, including prebiotics, helps promote a
                      healthy gut, aids digestion, and keeps you feeling full and satisfied.
                    </span>
                  </li>
                </ul>

                {/* Mobile Benefits Button */}
                <div className="mt-8 md:hidden">
                  <Link href="/benefits">
                    <Button className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 hover:shadow-lg">
                      <CheckCircle2 className="mr-2 h-5 w-5" />
                      Discover All Benefits
                    </Button>
                  </Link>
                </div>
              </div>
              <div className="relative h-[400px] overflow-hidden rounded-lg">
                <div
                  className="absolute inset-0 bg-cover bg-center"
                  style={{ backgroundImage: "url('/images/banana-benefits.png')" }}
                />
              </div>
            </div>
          </div>
        </section>

        {/* Final Battle Section */}
        <section id="battle" className="py-20 bg-amber-50">
          <div className="container">
            {/* Epic Battle Visualization */}
            <div className="mb-16 relative">
              <div className="flex justify-center">
                <div className="relative overflow-hidden rounded-2xl shadow-2xl border-4 border-amber-200">
                  <img
                    src="/images/BananavsTitan.png"
                    alt="Epic battle between banana industry titans - the ultimate showdown for market supremacy"
                    className="h-auto w-full max-w-5xl object-cover transition-all duration-700 hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-amber-900/40 via-transparent to-amber-900/20" />

                  {/* Battle Atmosphere Overlay */}
                  <div className="absolute top-4 left-4 right-4">
                    <div className="bg-black/60 backdrop-blur-sm rounded-lg px-4 py-2">
                      <p className="text-center text-amber-200 font-bold text-lg tracking-wide">
                        ⚔️ THE ULTIMATE SHOWDOWN ⚔️
                      </p>
                    </div>
                  </div>

                  {/* Battle Stakes */}
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="bg-gradient-to-r from-amber-600/90 to-yellow-600/90 backdrop-blur-sm rounded-lg px-6 py-3">
                      <p className="text-center text-white font-semibold text-sm">
                        "In the arena of global commerce, only one can claim the golden crown of banana supremacy!"
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Battle Preparation Text */}
              <div className="mt-8 text-center">
                <p className="text-amber-800 font-medium text-lg italic">
                  Witness the clash of titans that shaped the banana empire...
                </p>
              </div>
            </div>

            <div className="mb-12 flex flex-col items-center text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Final Battle</h2>
              <p className="mt-4 max-w-2xl text-lg text-muted-foreground">
                Test your knowledge of the titans of the banana industry. Who reigns supreme?
              </p>
            </div>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              <FactCard
                question="What Brand of Bananas is the most popular?"
                answer="Chiquita! With its iconic blue sticker and catchy jingle, Chiquita has been a household name since 1944, making it one of the most recognized and beloved banana brands worldwide."
                brandName="Chiquita"
                image="/images/chiquita-logo.png"
              />
              <FactCard
                question="What Brand of Banana sells the best?"
                answer="Dole stands as the world's largest producer of fruits and vegetables. Thanks to its massive scale and global distribution network, Dole sells more bananas than any other brand on the planet."
                brandName="Dole"
                image="/images/dole-logo.png"
              />
              <FactCard
                question="What Brand of Banana is the oldest on earth?"
                answer="Chiquita, originally the United Fruit Company founded in 1899, is the oldest major player. It pioneered the banana trade, transforming the fruit from a rare exotic treat into a global staple."
                brandName="United Fruit Company"
                image="/images/united-fruit-logo.png"
              />
            </div>
          </div>
        </section>

        {/* Time Travel Section */}
        <section id="time-travel" className="py-20">
          <div className="container">
            <div className="mb-12 flex flex-col items-center text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Travel in time to the year 2 AD</h2>
              <p className="mt-4 max-w-2xl text-lg text-muted-foreground">
                Caesar Augustus will have a banana with you.
              </p>
            </div>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <Card className="overflow-hidden">
                <div
                  className="h-[300px] bg-contain bg-center bg-no-repeat"
                  style={{ backgroundImage: "url('/images/BananasCaesar.png')" }}
                />
                <CardContent className="p-6">
                  <h3 className="mb-4 text-2xl font-bold">Dine with an Emperor</h3>
                  <div className="mb-3 flex items-start gap-3">
                    <Sparkles className="mt-0.5 h-5 w-5 flex-shrink-0 text-amber-500" />
                    <div>
                      <p className="font-medium">The Experience</p>
                      <p className="text-muted-foreground">
                        Step through our temporal portal to a lavish Roman villa. You'll be greeted by Caesar Augustus
                        himself, eager to taste this strange, sweet fruit from the future.
                      </p>
                    </div>
                  </div>
                  <p className="mt-4 text-sm italic text-muted-foreground">
                    Disclaimer: Our historians assure us this is *mostly* accurate. We've taken some creative liberties
                    for this unique encounter.
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <h3 className="mb-2 text-2xl font-bold">Cartoon of the week</h3>
                  <p className="mb-4 text-lg text-muted-foreground">Travel with our Friends around the world</p>
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <Calendar className="mt-0.5 h-5 w-5 flex-shrink-0 text-green-600" />
                      <div>
                        <p className="font-medium">Departures</p>
                        <p className="text-muted-foreground">Daily at sunrise and sunset.</p>
                      </div>
                    </div>
                    <Separator />
                    <div className="flex items-start gap-3">
                      <Clock className="mt-0.5 h-5 w-5 flex-shrink-0 text-green-600" />
                      <div>
                        <p className="font-medium">Duration</p>
                        <p className="text-muted-foreground">Approximately 2 hours (subjective time).</p>
                      </div>
                    </div>
                    <Separator />
                    <div className="flex items-start gap-3">
                      <Ticket className="mt-0.5 h-5 w-5 flex-shrink-0 text-green-600" />
                      <div>
                        <p className="font-medium">Cost</p>
                        <p className="text-muted-foreground">Priceless (and also free with any banana purchase).</p>
                      </div>
                    </div>
                  </div>
                  <Link href="#contact" className="w-full">
                    <Button className="mt-6 w-full bg-green-600 hover:bg-green-700">Book Now</Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Contact */}
        <section id="contact" className="py-20 bg-green-50">
          <div className="container">
            {/* Indiana Jones Adventure Image */}
            <div className="mb-12 flex justify-center">
              <div className="relative overflow-hidden rounded-xl shadow-xl">
                <img
                  src="/images/IndianaJones.png"
                  alt="Indiana Jones on an adventurous quest to find the best banana deals"
                  className="h-auto w-full max-w-2xl object-cover transition-all duration-500 hover:scale-110 hover:brightness-110"
                />
                <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/30" />
                <div className="absolute bottom-4 left-4 right-4">
                  <p className="text-center text-sm font-medium text-white drop-shadow-lg">
                    "It's not the years, honey... it's the bananas!" - Dr. Jones
                  </p>
                </div>
              </div>
            </div>

            <div className="text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
                Here we work for you to find the best deal on Bananas for the week.
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-muted-foreground">
                Like a true adventurer, we explore every corner of the market to uncover the finest banana treasures at unbeatable prices.
              </p>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t bg-muted/40">
        <div className="container py-10">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 md:grid-cols-3">
            <div>
              <h3 className="font-cinzel text-xl font-bold text-yellow-400">Bananas4011</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                The finest bananas and artisanal goods, delivered fresh since 2024.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold">Proud Partner</h3>
              <ul className="mt-2 space-y-2 text-sm">
                <li>
                  <Link href="https://fuse.vip" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-green-600">
                    Fuse.vip
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold">Follow Us</h3>
              <ul className="mt-2 space-y-2 text-sm">
                <li>
                  <Link href="https://x.com/bananas4011_?s=21" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-green-600">
                    Follow us on X!
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="mt-10 border-t pt-6 text-center text-sm text-muted-foreground">
            <p>© 2024 <span className="text-yellow-400 font-bold">Bananas4011</span>. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

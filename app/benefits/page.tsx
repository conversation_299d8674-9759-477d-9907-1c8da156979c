import Link from "next/link"
import { ChevronLeft, Heart, Brain, Award, CheckCircle2, Zap, Shield, Trophy, Star, Sparkles } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { NavBar } from "@/components/nav-bar"

export default function BenefitsPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <NavBar />

      <main className="flex-1">
        {/* Hero Section with BananaOlympia.png */}
        <section className="relative">
          <div className="absolute inset-0 z-10 bg-gradient-to-b from-black/60 via-black/40 to-black/70" />
          <div className="h-[700px] bg-cover bg-center" style={{ backgroundImage: "url('/images/BananaOlympia.png')" }} />
          <div className="container absolute inset-0 z-20 flex flex-col items-center justify-center text-center text-white">
            <div className="mb-6">
              <Badge className="bg-yellow-500/90 text-black font-bold text-lg px-4 py-2">
                🏆 CHAMPION'S CHOICE
              </Badge>
            </div>
            <h1 className="text-5xl font-bold tracking-tight sm:text-6xl md:text-7xl mb-6">
              Olympic-Level Benefits
            </h1>
            <p className="mt-6 max-w-3xl text-xl leading-relaxed">
              Discover why bananas are the ultimate performance fuel for champions. From physical vitality to mental clarity, 
              experience the triple advantage that sets Bananas4011 apart.
            </p>
            <div className="mt-10 flex flex-wrap justify-center gap-6">
              <Button size="lg" className="bg-yellow-500 hover:bg-yellow-600 text-black font-bold text-lg px-8 py-4">
                <Trophy className="mr-2 h-5 w-5" />
                Claim Your Victory
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-black font-bold text-lg px-8 py-4">
                Learn the Science
              </Button>
            </div>
          </div>
        </section>

        {/* Navigation Breadcrumb */}
        <section className="py-6 bg-muted/30">
          <div className="container">
            <Link href="/">
              <Button variant="ghost" className="gap-1 pl-0 text-muted-foreground hover:text-green-600">
                <ChevronLeft className="h-4 w-4" /> Back to Home
              </Button>
            </Link>
          </div>
        </section>

        {/* Introduction Section */}
        <section className="py-16 bg-gradient-to-b from-green-50 to-white">
          <div className="container">
            <div className="text-center max-w-4xl mx-auto">
              <h2 className="text-4xl font-bold tracking-tight mb-6">
                The Triple Advantage of Bananas
              </h2>
              <p className="text-xl text-muted-foreground leading-relaxed">
                Not all fruits are created equal. Bananas deliver a unique combination of physical wellness, 
                mental enhancement, and premium quality that makes them the champion's choice for peak performance.
              </p>
            </div>
          </div>
        </section>

        {/* Three Benefits Columns */}
        <section className="py-20">
          <div className="container">
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
              
              {/* Health Benefits Column */}
              <Card className="relative overflow-hidden border-2 border-green-200 hover:border-green-400 transition-all duration-300 hover:shadow-xl">
                <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-green-500 to-emerald-500" />
                <CardHeader className="text-center pb-4">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                    <Heart className="h-8 w-8 text-green-600" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-green-700">Health Benefits</CardTitle>
                  <p className="text-muted-foreground">Physical wellness and nutritional excellence</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <CheckCircle2 className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Potassium Powerhouse</h4>
                        <p className="text-sm text-muted-foreground">Essential for heart health and blood pressure regulation</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle2 className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Natural Energy Boost</h4>
                        <p className="text-sm text-muted-foreground">Complex carbs and natural sugars for sustained energy</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle2 className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Digestive Health</h4>
                        <p className="text-sm text-muted-foreground">Fiber and prebiotics for optimal gut function</p>
                      </div>
                    </div>
                  </div>
                  <div className="pt-4 border-t">
                    <Button className="w-full bg-green-600 hover:bg-green-700">
                      <Zap className="mr-2 h-4 w-4" />
                      Explore Health Benefits
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Mental Benefits Column */}
              <Card className="relative overflow-hidden border-2 border-blue-200 hover:border-blue-400 transition-all duration-300 hover:shadow-xl">
                <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-blue-500 to-indigo-500" />
                <CardHeader className="text-center pb-4">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                    <Brain className="h-8 w-8 text-blue-600" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-blue-700">Mental Benefits</CardTitle>
                  <p className="text-muted-foreground">Cognitive enhancement and mental clarity</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <CheckCircle2 className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Mood Enhancement</h4>
                        <p className="text-sm text-muted-foreground">Natural serotonin boost for better mood regulation</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle2 className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Cognitive Function</h4>
                        <p className="text-sm text-muted-foreground">B-vitamins support brain health and mental clarity</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle2 className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Stress Reduction</h4>
                        <p className="text-sm text-muted-foreground">Natural compounds help manage stress and anxiety</p>
                      </div>
                    </div>
                  </div>
                  <div className="pt-4 border-t">
                    <Button className="w-full bg-blue-600 hover:bg-blue-700">
                      <Brain className="mr-2 h-4 w-4" />
                      Discover Mental Benefits
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Bananas4011 Benefits Column */}
              <Card className="relative overflow-hidden border-2 border-yellow-200 hover:border-yellow-400 transition-all duration-300 hover:shadow-xl">
                <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-yellow-500 to-amber-500" />
                <CardHeader className="text-center pb-4">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-yellow-100">
                    <Award className="h-8 w-8 text-yellow-600" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-yellow-700">Bananas4011 Benefits</CardTitle>
                  <p className="text-muted-foreground">Premium quality and exclusive advantages</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <CheckCircle2 className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Premium Sourcing</h4>
                        <p className="text-sm text-muted-foreground">Hand-selected from the finest banana farms worldwide</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle2 className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Quality Guarantee</h4>
                        <p className="text-sm text-muted-foreground">Rigorous quality control for consistent excellence</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle2 className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Exclusive Experience</h4>
                        <p className="text-sm text-muted-foreground">Unique banana journey and educational content</p>
                      </div>
                    </div>
                  </div>
                  <div className="pt-4 border-t">
                    <Button className="w-full bg-yellow-600 hover:bg-yellow-700">
                      <Star className="mr-2 h-4 w-4" />
                      Experience Excellence
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Call to Action Section */}
        <section className="py-20 bg-gradient-to-r from-green-600 to-yellow-500">
          <div className="container text-center">
            <div className="max-w-3xl mx-auto text-white">
              <h2 className="text-4xl font-bold mb-6">Ready to Experience the Triple Advantage?</h2>
              <p className="text-xl mb-8 opacity-90">
                Join thousands of champions who have discovered the power of premium bananas for peak performance.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Link href="/#contact">
                  <Button size="lg" className="bg-white text-green-700 hover:bg-gray-100 font-bold text-lg px-8 py-4">
                    <Sparkles className="mr-2 h-5 w-5" />
                    Start Your Journey
                  </Button>
                </Link>
                <Link href="/">
                  <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-700 font-bold text-lg px-8 py-4">
                    Explore More Stories
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t bg-muted/40">
        <div className="container py-10">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 md:grid-cols-3">
            <div>
              <h3 className="font-cinzel text-xl font-bold text-yellow-400">Bananas4011</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                The finest bananas and artisanal goods, delivered fresh since 2024.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold">Proud Partner</h3>
              <ul className="mt-2 space-y-2 text-sm">
                <li>
                  <Link href="https://fuse.vip" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-green-600">
                    Fuse.vip
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold">Follow Us</h3>
              <ul className="mt-2 space-y-2 text-sm">
                <li>
                  <Link href="https://x.com/bananas4011_?s=21" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-green-600">
                    Follow us on X!
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="mt-10 border-t pt-6 text-center text-sm text-muted-foreground">
            <p>© 2024 <span className="text-yellow-400 font-bold">Bananas4011</span>. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

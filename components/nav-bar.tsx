import Link from "next/link"
import { CartIcon } from "@/components/cart-icon"

export function NavBar() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-6 md:gap-10">
          <Link href="/" className="flex items-center space-x-2">
            <span className="font-cinzel text-3xl font-bold tracking-wider text-yellow-400">Bananas4011</span>
          </Link>
          <nav className="hidden gap-6 md:flex">
            <Link href="/#about" className="text-sm font-medium transition-colors hover:text-green-600">
              Benefits
            </Link>
            <Link href="/#battle" className="text-sm font-medium transition-colors hover:text-green-600">
              Final Battle
            </Link>
            <Link href="/#time-travel" className="text-sm font-medium transition-colors hover:text-green-600">
              Time Travel
            </Link>
            <Link href="/#contact" className="text-sm font-medium transition-colors hover:text-green-600">
              Contact
            </Link>
          </nav>
        </div>
        <div className="flex items-center gap-2">
          <CartIcon />
        </div>
      </div>
    </header>
  )
}

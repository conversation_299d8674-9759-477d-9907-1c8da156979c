"use client"

import { Card, CardContent } from "@/components/ui/card"

interface FactCardProps {
  question: string
  answer: string
  brandName: string
  image: string
}

export function FactCard({ question, answer, brandName, image }: FactCardProps) {
  return (
    <div className="group h-80 w-full perspective">
      <div className="relative h-full w-full transform-style-3d transition-transform duration-700 group-hover:rotate-y-180">
        {/* Front of the card */}
        <div className="absolute h-full w-full backface-hidden">
          <Card className="flex h-full flex-col items-center justify-center p-6 text-center">
            <h3 className="text-xl font-bold">{question}</h3>
            <p className="mt-4 text-sm text-muted-foreground">Hover to reveal the answer</p>
          </Card>
        </div>

        {/* Back of the card */}
        <div className="absolute h-full w-full backface-hidden rotate-y-180">
          <Card className="relative h-full overflow-hidden">
            <div
              className="absolute inset-0 bg-cover bg-center transition-transform duration-300 group-hover:scale-105"
              style={{ backgroundImage: `url(${image})` }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-black/20" />
            <CardContent className="relative z-10 flex h-full flex-col justify-end p-6">
              <h3 className="text-2xl font-bold text-white">{brandName}</h3>
              <p className="mt-2 text-white/90">{answer}</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
